import React, { useCallback } from 'react'
import { StyledTextResource } from '@/types/resources'

import { StyledTextSelector } from '@/modules/video-editor/components/common/styled-text-selector'
import { useTextSettingContext } from './context'
import { buildTextOverlay } from '@/modules/video-editor/utils/text'
import { RotateCcw } from 'lucide-react'

const DefaultStyledText = () => {
  return (
    <div
      key="default-text"
      className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
    >
      <div className="h-full w-full flex items-center justify-center rounded">
        <RotateCcw />
      </div>
      <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
        清除样式
      </div>
    </div>
  )
}

export const StyledTextPanel = () => {
  const { textOverlay, requestUpdateText } = useTextSettingContext()

  const handleFontStyleSelect = useCallback(
    async (data: StyledTextResource.StyledText) => {
      if (!textOverlay) return

      return requestUpdateText(
        buildTextOverlay(data, { baseOverlay: textOverlay, isPreview: false }),
        true
      )
    }, [textOverlay, requestUpdateText])

  return (
    <div className="h-full flex flex-col">

      <div className="flex-1 overflow-hidden">
        <StyledTextSelector
          headerContent={[
            {
              node: <DefaultStyledText />,
              onClick: () => requestUpdateText({
                styles: {
                  strokeEnabled: false,
                  shadowEnabled: false,
                }
              }, true),
              key: 'action-btn'
            }
          ]}
          className="h-full flex flex-wrap"
          itemWrapper={(children, data) => (
            <div
              key={data.id}
              className="cursor-pointer"
              onClick={() => handleFontStyleSelect(data)}
            >
              {children}
            </div>
          )}
        />
      </div>
    </div>
  )
}
