import { MixcutRuleSwitch } from '@/modules/mixcut/components/mixcut-rule-switch'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut'
import { cn } from '@/components/lib/utils'
import { NumberInput } from '@/components/ui/number-input'
import React from 'react'
import { MixcutRulesFormCategories } from '../../context/useMixcutRulesForm'

export const GlobalTextTransform = () => {
  const CATEGORY = MixcutRulesFormCategories.globalText

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.globalText.positionOffset}
          label="全局文字位置偏移"
        >
          {({ getConfigValue, setConfigValue }) => (
            <div className="flex flex-col gap-4">
              <div className="flex gap-3 items-center">
                <div className={cn('text-sm font-normal cursor-pointer')}>偏移上限(px)</div>
                <NumberInput value={getConfigValue('range')} onChange={val => setConfigValue('range', val)} />
              </div>
            </div>
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.globalText.fontFamily}
          label="全局文字字体变换"
        >
          {({ setConfigValue }) => (
            <NarrationTextFontFamilySelector
              onChange={fonts => setConfigValue(
                'options',
                fonts.map(font => ({
                  fontSrc: font.content.url,
                  fontFamily: font.title
                }))
              )}
            />
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.globalText.styledText}
          label="全局文字花体字样式"
        >
          {({ setConfigValue }) => (
            <NarrationTextStyledTextSelector
              onChange={items => {
                setConfigValue(
                  'options',
                  items.map(item => ({
                    backgroundColor: item.content.backgroundColor,
                    textColor: item.content.textColor,
                    borderWidth: item.content.borderWidth,
                    borderColor: item.content.borderColor,
                    shadowDistance: item.content.shadowDistance,
                    shadowAngle: item.content.shadowAngle,
                    shadowBlur: item.content.shadowBlur,
                    shadowColor: item.content.shadowColor,
                    shadowColorAlpha: item.content.shadowColorAlpha,
                  }))
                )
              }}
            />
          )}
        </MixcutRuleSwitch>

      </div>
    </div>
  )
}
