import { MixcutRulesFormCategories } from '@/modules/mixcut/context/useMixcutRulesForm'
import { MixcutRuleSwitch } from '@/modules/mixcut/components/mixcut-rule-switch'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut'
import { Checkbox, LabeledCheckbox } from '@/components/ui/checkbox'
import { cn } from '@/components/lib/utils'
import { NumberInput } from '@/components/ui/number-input'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useInfiniteQueryFontUnified } from '@/hooks/queries/useQueryFont'
import InfiniteResourceList from '@/components/InfiniteResourceList'
import { FontResource, StyledTextResource } from '@/types/resources'
import { Input } from '@/components/ui/input'
import { throttle } from 'lodash'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import { Label } from '@/components/ui/label'
import { CheckedState } from '@radix-ui/react-checkbox'
import { InfiniteData, UseInfiniteQueryResult } from '@tanstack/react-query'
import { PaginatedResult } from '@app/shared/infra/request'
import { useInfiniteQueryFontStyleList } from '@/hooks/queries/useQueryTextStyle'
import { StyledTextSelector } from '@/modules/video-editor/components/common/styled-text-selector'

const FontPreviewItem: React.FC<{ font: FontResource.Font; className?: string }> = ({
  font, className = ''
}) => {
  if (!font.cover?.url) return null

  return (
    <div
      className={cn(
        `flex items-center justify-center py-1.5 border border-gray-400
        rounded-md transition-colors`,
        className
      )}
    >
      {/* 字体预览图 */}
      <img
        src={font.cover.url}
        alt={`${font.title} 预览`}
        className="h-7 aspect-auto object-cover"
      />
    </div>
  )
}

function SelectableInfinitePanel<TItem extends { id: number }>(props: {
  onChange?: (val: TItem[]) => void,
  renderItem(item: TItem): React.ReactNode
  scrollAreaHeight?: number
  itemName?: string
  query: UseInfiniteQueryResult<InfiniteData<PaginatedResult<TItem>, unknown>, Error>
}) {
  const { query, renderItem, onChange, itemName = '选项', scrollAreaHeight = 60 * 4 } = props

  const checkboxId = 'NarrationTextFontFamilySelector'
  const [selected, setSelected] = useState(new Map<number, TItem>())

  const _renderItem = useCallback((item: TItem) => {
    return (
      <div
        className={cn(
          'cursor-pointer rounded-md border-2 flex justify-center',
          selected.has(item.id) && 'border-primary-accent/50'
        )}
        onClick={() => setSelected(prev => {
          const newMap = new Map(prev)
          if (prev.has(item.id)) {
            newMap.delete(item.id)
          } else {
            newMap.set(item.id, item)
          }

          return newMap
        })}
      >
        {renderItem(item)}
      </div>
    )
  }, [selected.keys(), renderItem])

  const checked = useMemo<CheckedState>(() => {
    if (!query?.data?.pages?.length) return false

    if (selected.size === 0) return false

    if (selected.size === query.data.pages[0].total) return true

    return 'indeterminate'
  }, [selected, query.data])

  const handleCheckedChange = (checked: CheckedState) => {
    if (checked === true) {
      const newMap = new Map()

      query.data?.pages.forEach(page => {
        page.list.forEach(font => {
          if (newMap.has(font.id)) return
          newMap.set(font.id, font)
        })
      })

      setSelected(newMap)
    } else {
      setSelected(new Map())
    }
  }

  useEffect(() => {
    onChange?.(Array.from(selected.values()))
  }, [selected])

  return (
    <div>
      <div className={cn('flex items-center space-x-2 mt-3')}>
        <Checkbox
          id={checkboxId}
          checked={checked}
          onCheckedChange={handleCheckedChange}
        />
        <Label htmlFor={checkboxId} className={cn('text-sm font-normal cursor-pointer')}>
          已选择 {selected.size} {`个${itemName}`}
        </Label>
      </div>

      <div className="overflow-y-auto" style={{ height: scrollAreaHeight }}>
        <div className="p-1 space-y-1">
          <InfiniteResourceList
            queryResult={query}
            renderItem={_renderItem}
            itemsContainerClassName="p-2 grid grid-cols-3 gap-2"
          />
        </div>
      </div>
    </div>
  )
}

const NarrationTextFontFamilySelector: React.FC<{ onChange?: (val: FontResource.Font[]) => void }> = ({ onChange }) => {
  const [keywordInput, setKeywordInput] = useState('')

  const keyword = useDebounceValue(keywordInput, 500)

  const fontInfiniteQueryResult = useInfiniteQueryFontUnified({
    keyword,
    pageNo: 1,
    pageSize: 99,
    autoLoadAllPages: true
  })

  return (
    <div>
      <Input
        value={keywordInput}
        onChange={throttle(e => setKeywordInput(e.target.value), 500)}
        placeholder="搜索字体"
      />

      <SelectableInfinitePanel
        query={fontInfiniteQueryResult}
        itemName="字体"
        onChange={onChange}
        renderItem={item => (
          <FontPreviewItem
            font={item}
            className="border-none"
          />
        )}
      />
    </div>
  )
}

const NarrationTextStyledTextSelector: React.FC<{ onChange?: (val: StyledTextResource.StyledText[]) => void }> = ({ onChange }) => {
  const query = useInfiniteQueryFontStyleList({
    pageNo: 1,
    pageSize: 50,
    autoLoadAllPages: true
  })

  return (
    <div>
      <SelectableInfinitePanel
        query={query}
        itemName="样式"
        scrollAreaHeight={368}
        onChange={onChange}
        renderItem={item => (
          <StyledTextSelector.ItemRenderer
            {...item}
            className="border-none"
          />
        )}
      />
    </div>
  )
}

export const NarrationTextTransform = () => {
  const CATEGORY = MixcutRulesFormCategories.narrationText

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.narrationText.positionOffset}
          label="口播字幕位置偏移"
        >
          {({ getConfigValue, setConfigValue }) => (
            <div className="flex flex-col gap-4">
              <LabeledCheckbox
                id="allow-horizontal-offset"
                label="允许水平偏移"
                checked={getConfigValue('allowHorizontalOffset')}
                onChange={checked => setConfigValue('allowHorizontalOffset', checked)}
              />
              <div className="flex gap-3 items-center">
                <div className={cn('text-sm font-normal cursor-pointer')}>偏移上限(px)</div>
                <NumberInput value={getConfigValue('range')} onChange={val => setConfigValue('range', val)} />
              </div>
            </div>
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.narrationText.fontFamily}
          label="口播字幕字体变换"
        >
          {({ setConfigValue }) => (
            <NarrationTextFontFamilySelector
              onChange={fonts => setConfigValue(
                'options',
                fonts.map(font => ({
                  fontSrc: font.content.url,
                  fontFamily: font.title
                }))
              )}
            />
          )}
        </MixcutRuleSwitch>

        <MixcutRuleSwitch
          category={CATEGORY}
          pipeline={MIXCUT_PIPELINES.narrationText.styledText}
          label="口播字幕花体字样式"
        >
          {({ setConfigValue }) => (
            <NarrationTextStyledTextSelector
              onChange={items => {
                setConfigValue(
                  'options',
                  items.map(item => ({
                    backgroundColor: item.content.backgroundColor,
                    textColor: item.content.textColor,
                    borderWidth: item.content.borderWidth,
                    borderColor: item.content.borderColor,
                    shadowDistance: item.content.shadowDistance,
                    shadowAngle: item.content.shadowAngle,
                    shadowBlur: item.content.shadowBlur,
                    shadowColor: item.content.shadowColor,
                    shadowColorAlpha: item.content.shadowColorAlpha,
                  }))
                )
              }}
            />
          )}
        </MixcutRuleSwitch>

      </div>
    </div>
  )
}
