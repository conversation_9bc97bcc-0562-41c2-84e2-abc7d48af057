import { MIXCUT_PIPELINES, MixcutPipelineSchemas } from '@app/shared/types/mixcut.js'
import { MixcutPipelineContext, SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import z from 'zod'
import { MixcutPipelineUtils } from '@/modules/mixcut/pipeline/mixcut-pipeline.utils.js'

type AcceptablePipeline =
  | typeof MIXCUT_PIPELINES.globalText.styledText
  | typeof MIXCUT_PIPELINES.narrationText.styledText

export class TextStyledTextPipeline extends SimpleMixcutPipeline<AcceptablePipeline, TextOverlay> {

  // narrationText 模式下，在构造函数中选择一次样式配置，所有 overlay 使用相同样式
  private readonly schema?: typeof MixcutPipelineSchemas.textStyledTextSchema
  private readonly config?: z.infer<typeof MixcutPipelineSchemas.textStyledTextSchema>['options'][number]

  constructor(
    context: MixcutPipelineContext<AcceptablePipeline>,
  ) {
    super(context)

    // 如果是 narrationText 模式，在构造函数中选择样式配置
    if (this._isNarrationTextPipeline()) {
      this.schema = MixcutPipelineSchemas.schemaByPipeline[MIXCUT_PIPELINES.narrationText.styledText]
      this.config = _.sample(context.params.options)
    }
  }

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay) {
    if (this._isNarrationTextPipeline()) {
      // narrationText 模式：使用构造函数中选择的样式配置
      if (!this.config) return overlay

      return {
        ...overlay,
        styles: MixcutPipelineUtils.mergeStyles(overlay.styles, this.config)
      }
    } else {
      // globalText 模式：每次随机选择样式配置
      const config = _.sample(this.context.params.options)
      if (!config) return overlay

      return {
        ...overlay,
        styles: MixcutPipelineUtils.mergeStyles(overlay.styles, config)
      }
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    if (this._isNarrationTextPipeline()) {
      return SimpleMixcutPipeline.filters.narrationText(overlay)
    } else {
      return SimpleMixcutPipeline.filters.globalText(overlay)
    }
  }

  /**
   * 判断当前管道是否为 narrationText 类型
   */
  private _isNarrationTextPipeline(): boolean {
    return this.context.pipeline === MIXCUT_PIPELINES.narrationText.styledText
  }
}
