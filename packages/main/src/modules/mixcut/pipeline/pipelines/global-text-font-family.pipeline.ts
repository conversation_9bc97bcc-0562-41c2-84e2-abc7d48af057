import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { MixcutPipelineContext, SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'

export class GlobalTextFontFamilyPipeline extends SimpleMixcutPipeline<typeof MIXCUT_PIPELINES.globalText.fontFamily, TextOverlay> {

  constructor(
    context: MixcutPipelineContext<typeof MIXCUT_PIPELINES.globalText.fontFamily>,
  ) {
    super(context)
  }

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay) {
    if (!this.context.params.options?.length) return overlay

    const { fontSrc, fontFamily } = _.sample(this.context.params.options)!

    return {
      ...overlay,
      src: fontSrc,
      styles: {
        ...overlay.styles,
        fontFamily: fontFamily
      }
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    return SimpleMixcutPipeline.filters.globalText(overlay)
  }
}
