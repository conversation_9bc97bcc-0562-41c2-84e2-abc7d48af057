import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { MixcutPipelineContext, SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'

type AcceptablePipeline =
  | typeof MIXCUT_PIPELINES.globalText.fontFamily
  | typeof MIXCUT_PIPELINES.narrationText.fontFamily

export class TextFontFamilyPipeline extends SimpleMixcutPipeline<AcceptablePipeline, TextOverlay> {

  // narrationText 模式下，在构造函数中选择一次字体，所有 overlay 使用相同字体
  private readonly fontSrc?: string
  private readonly fontFamily?: string

  constructor(
    context: MixcutPipelineContext<AcceptablePipeline>,
  ) {
    super(context)

    // 如果是 narrationText 模式，在构造函数中选择字体
    if (this._isNarrationTextPipeline()) {
      const { fontSrc, fontFamily } = _.sample(context.params.options) || {}
      this.fontSrc = fontSrc
      this.fontFamily = fontFamily
    }
  }

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay) {
    if (this._isNarrationTextPipeline()) {
      // narrationText 模式：使用构造函数中选择的字体
      if (!this.fontSrc || !this.fontFamily) return overlay

      return {
        ...overlay,
        src: this.fontSrc,
        styles: {
          ...overlay.styles,
          fontFamily: this.fontFamily
        }
      }
    } else {
      // globalText 模式：每次随机选择字体
      if (!this.context.params.options?.length) return overlay

      const { fontSrc, fontFamily } = _.sample(this.context.params.options)!

      return {
        ...overlay,
        src: fontSrc,
        styles: {
          ...overlay.styles,
          fontFamily: fontFamily
        }
      }
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    if (this._isNarrationTextPipeline()) {
      return SimpleMixcutPipeline.filters.narrationText(overlay)
    } else {
      return SimpleMixcutPipeline.filters.globalText(overlay)
    }
  }

  /**
   * 判断当前管道是否为 narrationText 类型
   */
  private _isNarrationTextPipeline(): boolean {
    return this.context.pipeline === MIXCUT_PIPELINES.narrationText.fontFamily
  }
}
