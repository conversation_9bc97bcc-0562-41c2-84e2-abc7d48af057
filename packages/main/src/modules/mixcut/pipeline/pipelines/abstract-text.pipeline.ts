import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'

/**
 * 所有文本管道的联合类型
 */
export type TextPipelineType = 
  | typeof MIXCUT_PIPELINES.globalText.fontFamily
  | typeof MIXCUT_PIPELINES.globalText.positionOffset
  | typeof MIXCUT_PIPELINES.globalText.styledText
  | typeof MIXCUT_PIPELINES.narrationText.fontFamily
  | typeof MIXCUT_PIPELINES.narrationText.positionOffset
  | typeof MIXCUT_PIPELINES.narrationText.styledText

/**
 * 文本管道功能类型枚举
 */
export enum TextPipelineFunction {
  FONT_FAMILY = 'fontFamily',
  POSITION_OFFSET = 'positionOffset',
  STYLED_TEXT = 'styledText'
}

/**
 * 文本管道目标类型枚举
 */
export enum TextPipelineTarget {
  GLOBAL_TEXT = 'globalText',
  NARRATION_TEXT = 'narrationText'
}

/**
 * 抽象文本管道基类
 * 封装所有文本管道的通用逻辑，包括类型判断、过滤器选择等
 */
export abstract class AbstractTextPipeline extends SimpleMixcutPipeline<TextPipelineType, TextOverlay> {

  /**
   * 获取当前管道的功能类型
   */
  protected getPipelineFunction(): TextPipelineFunction {
    const pipeline = this.context.pipeline
    
    if (pipeline === MIXCUT_PIPELINES.globalText.fontFamily || 
        pipeline === MIXCUT_PIPELINES.narrationText.fontFamily) {
      return TextPipelineFunction.FONT_FAMILY
    }
    
    if (pipeline === MIXCUT_PIPELINES.globalText.positionOffset || 
        pipeline === MIXCUT_PIPELINES.narrationText.positionOffset) {
      return TextPipelineFunction.POSITION_OFFSET
    }
    
    if (pipeline === MIXCUT_PIPELINES.globalText.styledText || 
        pipeline === MIXCUT_PIPELINES.narrationText.styledText) {
      return TextPipelineFunction.STYLED_TEXT
    }
    
    throw new Error(`Unknown text pipeline: ${pipeline}`)
  }

  /**
   * 获取当前管道的目标类型
   */
  protected getPipelineTarget(): TextPipelineTarget {
    const pipeline = this.context.pipeline
    
    if (pipeline === MIXCUT_PIPELINES.globalText.fontFamily || 
        pipeline === MIXCUT_PIPELINES.globalText.positionOffset || 
        pipeline === MIXCUT_PIPELINES.globalText.styledText) {
      return TextPipelineTarget.GLOBAL_TEXT
    }
    
    if (pipeline === MIXCUT_PIPELINES.narrationText.fontFamily || 
        pipeline === MIXCUT_PIPELINES.narrationText.positionOffset || 
        pipeline === MIXCUT_PIPELINES.narrationText.styledText) {
      return TextPipelineTarget.NARRATION_TEXT
    }
    
    throw new Error(`Unknown text pipeline: ${pipeline}`)
  }

  /**
   * 判断当前管道是否为 narrationText 类型
   */
  protected isNarrationTextPipeline(): boolean {
    return this.getPipelineTarget() === TextPipelineTarget.NARRATION_TEXT
  }

  /**
   * 判断当前管道是否为 globalText 类型
   */
  protected isGlobalTextPipeline(): boolean {
    return this.getPipelineTarget() === TextPipelineTarget.GLOBAL_TEXT
  }

  /**
   * 统一的过滤器实现
   */
  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    if (this.isNarrationTextPipeline()) {
      return SimpleMixcutPipeline.filters.narrationText(overlay)
    } else {
      return SimpleMixcutPipeline.filters.globalText(overlay)
    }
  }
}
