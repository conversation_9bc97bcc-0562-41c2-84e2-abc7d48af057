import { MixcutPipelineContext } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import { 
  AbstractTextPipeline, 
  TextPipelineType, 
  TextPipelineFunction, 
  TextPipelineTarget 
} from './abstract-text.pipeline.js'
import { TextFontFamilyPipeline } from './text-font-family.pipeline.js'
import { TextPositionPipeline } from './text-position.pipeline.js'
import { TextStyledTextPipeline } from './text-styled-text.pipeline.js'

/**
 * 统一文本管道处理器
 * 能够处理所有六个文本管道的注册和执行，通过策略模式调用具体的处理逻辑
 */
export class UnifiedTextPipeline extends AbstractTextPipeline {

  private readonly delegateProcessor: AbstractTextPipeline

  constructor(context: MixcutPipelineContext<TextPipelineType>) {
    super(context)
    
    // 根据管道功能类型创建对应的具体处理器
    const pipelineFunction = this.getPipelineFunction()
    
    switch (pipelineFunction) {
      case TextPipelineFunction.FONT_FAMILY:
        this.delegateProcessor = new TextFontFamilyPipeline(context as any)
        break
        
      case TextPipelineFunction.POSITION_OFFSET:
        this.delegateProcessor = new TextPositionPipeline(context as any)
        break
        
      case TextPipelineFunction.STYLED_TEXT:
        this.delegateProcessor = new TextStyledTextPipeline(context as any)
        break
        
      default:
        throw new Error(`Unsupported pipeline function: ${pipelineFunction}`)
    }
  }

  /**
   * 委托给具体的处理器执行单个 overlay 的处理
   */
  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay): Promise<TextOverlay & MixcutableOverlay> {
    return this.delegateProcessor.processSingleOverlay(overlay)
  }

  /**
   * 使用基类的统一过滤器实现
   * 这里不需要重写，直接继承 AbstractTextPipeline 的 filter 方法
   */
}
