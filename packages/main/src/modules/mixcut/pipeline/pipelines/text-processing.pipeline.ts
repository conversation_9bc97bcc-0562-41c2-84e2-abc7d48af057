import { MIXCUT_PIPELINES, MixcutPipelineSchemas, ParamsForPipeline } from '@app/shared/types/mixcut.js'
import { MixcutPipelineContext, SimpleMixcutPipeline } from '../mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import { MixcutPipelineUtils } from '@/modules/mixcut/pipeline/mixcut-pipeline.utils.js'
import _ from 'lodash'
import z from 'zod'

/**
 * 所有文本管道的联合类型
 */
export type TextPipelineType =
  | typeof MIXCUT_PIPELINES.globalText.fontFamily
  | typeof MIXCUT_PIPELINES.globalText.positionOffset
  | typeof MIXCUT_PIPELINES.globalText.styledText
  | typeof MIXCUT_PIPELINES.narrationText.fontFamily
  | typeof MIXCUT_PIPELINES.narrationText.positionOffset
  | typeof MIXCUT_PIPELINES.narrationText.styledText

/**
 * 文本管道功能类型枚举
 */
export enum TextPipelineFunction {
  FONT_FAMILY = 'fontFamily',
  POSITION_OFFSET = 'positionOffset',
  STYLED_TEXT = 'styledText'
}

/**
 * 文本管道目标类型枚举
 */
export enum TextPipelineTarget {
  GLOBAL_TEXT = 'globalText',
  NARRATION_TEXT = 'narrationText'
}

/**
 * 统一文本处理管道
 * 直接实现所有六种文本管道的处理逻辑，不依赖外部实现
 */
export class TextProcessingPipeline extends SimpleMixcutPipeline<TextPipelineType, TextOverlay> {

  // narrationText 模式下的预选配置，在构造函数中选择一次，所有 overlay 使用相同配置
  private readonly narrationFontSrc?: string
  private readonly narrationFontFamily?: string
  private readonly narrationStyledConfig?: z.infer<typeof MixcutPipelineSchemas.textStyledTextSchema>['options'][number]

  constructor(context: MixcutPipelineContext<TextPipelineType>) {
    super(context)

    // 如果是 narrationText 模式，在构造函数中预选配置
    if (this.isNarrationTextPipeline()) {
      // const pipelineFunction = this.getPipelineFunction()
      const [pipelineFunction, params] = this.determinePipelineFunction()

      if (pipelineFunction === TextPipelineFunction.FONT_FAMILY) {
        const { fontSrc, fontFamily } = _.sample(params.options) || {}
        this.narrationFontSrc = fontSrc
        this.narrationFontFamily = fontFamily
      } else if (pipelineFunction === TextPipelineFunction.STYLED_TEXT) {
        this.narrationStyledConfig = _.sample(params.options)
      }
    }
  }

  /**
   * 核心处理方法：根据管道类型分发到具体的处理逻辑
   */
  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay): Promise<TextOverlay & MixcutableOverlay> {
    const [pipelineFunction, params] = this.determinePipelineFunction()

    switch (pipelineFunction) {
      case TextPipelineFunction.FONT_FAMILY:
        return this.processFontFamily(overlay, params.options)
      case TextPipelineFunction.POSITION_OFFSET:
        return this.processPositionOffset(overlay, params)
      case TextPipelineFunction.STYLED_TEXT:
        return this.processStyledText(overlay, params.options)
      default:
        throw new Error(`Unsupported pipeline function: ${pipelineFunction}`)
    }
  }

  /**
   * 处理字体管道逻辑
   */
  private async processFontFamily(
    overlay: TextOverlay & MixcutableOverlay,
    options: z.infer<typeof MixcutPipelineSchemas.textFontFamilySchema>['options']
  ): Promise<TextOverlay & MixcutableOverlay> {
    if (this.isNarrationTextPipeline()) {
      // narrationText 模式：使用构造函数中选择的字体
      if (!this.narrationFontSrc || !this.narrationFontFamily) return overlay

      return {
        ...overlay,
        src: this.narrationFontSrc,
        styles: {
          ...overlay.styles,
          fontFamily: this.narrationFontFamily
        }
      }
    } else {
      // globalText 模式：每次随机选择字体
      if (!options?.length) return overlay

      const { fontSrc, fontFamily } = _.sample(options)!

      return {
        ...overlay,
        src: fontSrc,
        styles: {
          ...overlay.styles,
          fontFamily: fontFamily
        }
      }
    }
  }

  /**
   * 处理位置偏移管道逻辑
   */
  private async processPositionOffset(
    overlay: TextOverlay & MixcutableOverlay,
    params: ParamsForPipeline<typeof MIXCUT_PIPELINES.globalText.positionOffset | typeof MIXCUT_PIPELINES.narrationText.positionOffset>
  ): Promise<TextOverlay & MixcutableOverlay> {
    const { minX, minY, maxX, maxY } = this.getOffsetBoundary()
    const { range } = params

    let offsetX: number

    if (this.isNarrationTextPipeline()) {
      // narrationText 模式：检查是否允许水平偏移
      const { allowHorizontalOffset } = params as ParamsForPipeline<typeof MIXCUT_PIPELINES.narrationText.positionOffset>
      offsetX = !allowHorizontalOffset
        ? 0
        : _.clamp(
          _.random(-range, range),
          minX,
          maxX
        )
    } else {
      // globalText 模式：总是允许水平偏移
      offsetX = _.clamp(
        _.random(-range, range),
        minX,
        maxX
      )
    }

    const offsetY = _.clamp(
      _.random(-range, range),
      minY,
      maxY
    )

    return {
      ...overlay,
      left: overlay.left + offsetX,
      top: overlay.top + offsetY,
    }
  }

  /**
   * 处理样式文本管道逻辑
   */
  private async processStyledText(
    overlay: TextOverlay & MixcutableOverlay,
    options: z.infer<typeof MixcutPipelineSchemas.textStyledTextSchema>['options']
  ): Promise<TextOverlay & MixcutableOverlay> {
    if (this.isNarrationTextPipeline()) {
      // narrationText 模式：使用构造函数中选择的样式配置
      if (!this.narrationStyledConfig) return overlay

      return {
        ...overlay,
        styles: MixcutPipelineUtils.mergeStyles(overlay.styles, this.narrationStyledConfig)
      }
    } else {
      // globalText 模式：每次随机选择样式配置
      const config = _.sample(options)
      if (!config) return overlay

      return {
        ...overlay,
        styles: MixcutPipelineUtils.mergeStyles(overlay.styles, config)
      }
    }
  }

  /**
   * 获取当前管道的功能类型
   */
  private getPipelineFunction(): TextPipelineFunction {
    const pipeline = this.context.pipeline

    if (pipeline === MIXCUT_PIPELINES.globalText.fontFamily ||
        pipeline === MIXCUT_PIPELINES.narrationText.fontFamily) {
      return TextPipelineFunction.FONT_FAMILY
    }

    if (pipeline === MIXCUT_PIPELINES.globalText.positionOffset ||
        pipeline === MIXCUT_PIPELINES.narrationText.positionOffset) {
      return TextPipelineFunction.POSITION_OFFSET
    }

    if (pipeline === MIXCUT_PIPELINES.globalText.styledText ||
        pipeline === MIXCUT_PIPELINES.narrationText.styledText) {
      return TextPipelineFunction.STYLED_TEXT
    }

    throw new Error(`Unknown text pipeline: ${pipeline}`)
  }

  private determinePipelineFunction() {
    const pipeline = this.context.pipeline

    if (pipeline === MIXCUT_PIPELINES.globalText.fontFamily
      || pipeline === MIXCUT_PIPELINES.narrationText.fontFamily
    ) {
      return [TextPipelineFunction.FONT_FAMILY, this.context.params as ParamsForPipeline<typeof pipeline>] as const
    }

    if (pipeline === MIXCUT_PIPELINES.globalText.positionOffset
      || pipeline === MIXCUT_PIPELINES.narrationText.positionOffset
    ) {
      return [TextPipelineFunction.POSITION_OFFSET, this.context.params as ParamsForPipeline<typeof pipeline>] as const
    }

    if (pipeline === MIXCUT_PIPELINES.globalText.styledText
      || pipeline === MIXCUT_PIPELINES.narrationText.styledText
    ) {
      return [TextPipelineFunction.STYLED_TEXT, this.context.params as ParamsForPipeline<typeof pipeline>] as const
    }

    throw new Error(`Unknown text pipeline: ${pipeline}`)
  }

  /**
   * 获取当前管道的目标类型
   */
  private getPipelineTarget(): TextPipelineTarget {
    const pipeline = this.context.pipeline

    if (pipeline === MIXCUT_PIPELINES.globalText.fontFamily ||
        pipeline === MIXCUT_PIPELINES.globalText.positionOffset ||
        pipeline === MIXCUT_PIPELINES.globalText.styledText) {
      return TextPipelineTarget.GLOBAL_TEXT
    }

    if (pipeline === MIXCUT_PIPELINES.narrationText.fontFamily ||
        pipeline === MIXCUT_PIPELINES.narrationText.positionOffset ||
        pipeline === MIXCUT_PIPELINES.narrationText.styledText) {
      return TextPipelineTarget.NARRATION_TEXT
    }

    throw new Error(`Unknown text pipeline: ${pipeline}`)
  }

  /**
   * 判断当前管道是否为 narrationText 类型
   */
  private isNarrationTextPipeline(): boolean {
    return this.getPipelineTarget() === TextPipelineTarget.NARRATION_TEXT
  }

  /**
   * 获取位置偏移的边界值
   */
  private getOffsetBoundary() {
    return MixcutPipelineUtils.getOverlaysBoundary(
      this.context.overlays.filter(o => this.filter(o)),
      this.context.playerMetadata.width,
      this.context.playerMetadata.height
    )
  }

  /**
   * 统一的过滤器实现
   */
  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    if (this.isNarrationTextPipeline()) {
      return SimpleMixcutPipeline.filters.narrationText(overlay)
    } else {
      return SimpleMixcutPipeline.filters.globalText(overlay)
    }
  }
}
