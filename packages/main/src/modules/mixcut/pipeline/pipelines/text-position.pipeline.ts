import { SimpleMixcutPipeline } from '@/modules/mixcut/pipeline/mixcut-pipeline.types.js'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { TextOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'
import { MIXCUT_PIPELINES } from '@app/shared/types/mixcut.js'
import { MixcutPipelineUtils } from '@/modules/mixcut/pipeline/mixcut-pipeline.utils.js'

type AcceptablePipeline =
  | typeof MIXCUT_PIPELINES.globalText.positionOffset
  | typeof MIXCUT_PIPELINES.narrationText.positionOffset

export class TextPositionPipeline extends SimpleMixcutPipeline<AcceptablePipeline, TextOverlay> {

  async processSingleOverlay(overlay: TextOverlay & MixcutableOverlay): Promise<TextOverlay & MixcutableOverlay> {
    const { minX, minY, maxX, maxY } = this._offsetBoundary

    const { range } = this.context.params

    let offsetX: number

    if (this._isNarrationTextPipeline()) {
      // narrationText 模式：检查是否允许水平偏移
      const { allowHorizontalOffset } = this.context.params as any
      offsetX = !allowHorizontalOffset
        ? 0
        : _.clamp(
          _.random(-range, range),
          minX,
          maxX
        )
    } else {
      // globalText 模式：总是允许水平偏移
      offsetX = _.clamp(
        _.random(-range, range),
        minX,
        maxX
      )
    }

    const offsetY = _.clamp(
      _.random(-range, range),
      minY,
      maxY
    )

    return {
      ...overlay,
      left: overlay.left + offsetX,
      top: overlay.top + offsetY,
    }
  }

  filter(overlay: MixcutableOverlay): overlay is TextOverlay & MixcutableOverlay {
    if (this._isNarrationTextPipeline()) {
      return SimpleMixcutPipeline.filters.narrationText(overlay)
    } else {
      return SimpleMixcutPipeline.filters.globalText(overlay)
    }
  }

  private get _offsetBoundary() {
    return MixcutPipelineUtils.getOverlaysBoundary(
      this.context.overlays.filter(o => this.filter(o)),
      this.context.playerMetadata.width,
      this.context.playerMetadata.height
    )
  }

  /**
   * 判断当前管道是否为 narrationText 类型
   */
  private _isNarrationTextPipeline(): boolean {
    return this.context.pipeline === MIXCUT_PIPELINES.narrationText.positionOffset
  }
}
