import { z } from 'zod'

export const MIXCUT_PIPELINES = {
  video: {
    rotation: 'VIDEO_ROTATION',
    scale: 'VIDEO_SCALE',
    positionOffset: 'VIDEO_POSITION_OFFSET',
    flip: 'VIDEO_FLIP',
    smartClip: 'VIDEO_SMART_CLIP',
    speed: 'VIDEO_SPEED',
    trim: 'VIDEO_TRIM',
    masking: 'VIDEO_MASKING',
  },
  narrationText: {
    positionOffset: 'NARRATION_POSITION_OFFSET',
    fontFamily: 'NARRATION_FONT_FAMILY',
    styledText: 'NARRATION_STYLED_TEXT',
  },
  globalText: {
    positionOffset: 'GLOBAL_TEXT_POSITION_OFFSET',
    fontFamily: 'GLOBAL_TEXT_FONT_FAMILY',
    styledText: 'GLOBAL_TEXT_STYLED_TEXT',
  },
  narrationSound: {
    timbre: 'NARRATION_TIMBRE',
  },
} as const

export type MixcutPipelineCategories = keyof typeof MIXCUT_PIPELINES

export type MixcutPipelines =
  | (typeof MIXCUT_PIPELINES.video)[keyof typeof MIXCUT_PIPELINES.video]
  | (typeof MIXCUT_PIPELINES.globalText)[keyof typeof MIXCUT_PIPELINES.globalText]
  | (typeof MIXCUT_PIPELINES.narrationText)[keyof typeof MIXCUT_PIPELINES.narrationText]
  | (typeof MIXCUT_PIPELINES.narrationSound)[keyof typeof MIXCUT_PIPELINES.narrationSound]

export namespace MixcutPipelineSchemas {
  const base = z.object({
    enabled: z.boolean().default(false),
  })

  const textPositionOffsetSchema = base.extend({
    range: z.number().default(32)
  })

  const textFontFamilySchema = base.extend({
    options: z
      .array(
        z.object({
          fontFamily: z.string(),
          fontSrc: z.string(),
        })
      )
      .default([])
  })

  export const textStyledTextSchema = base.extend({
    options: z
      .array(
        z.object({
          backgroundColor: z.string().optional(),
          textColor: z.string().optional(),

          borderWidth: z.number().optional(),
          borderColor: z.string().optional(),

          shadowDistance: z.number().optional(),
          shadowAngle: z.number().optional(),
          shadowBlur: z.number().optional(),
          shadowColor: z.string().optional(),
          shadowColorAlpha: z.number().optional(),
        })
      )
      .default([])
  })

  export const schemaByPipeline = {
    //#region ~ 视频
    [MIXCUT_PIPELINES.video.rotation]: base.extend({
      range: z.number().default(7.5)
    }),
    [MIXCUT_PIPELINES.video.scale]: base.extend({
      range: z.number().default(0.2)
    }),
    [MIXCUT_PIPELINES.video.positionOffset]: base.extend({
      range: z.number().default(0.25)
    }),
    [MIXCUT_PIPELINES.video.trim]: base.extend({
      rangeMin: z.number().default(0),
      rangeMax: z.number().default(15),
      allowTrimStart: z.boolean().default(true),
    }),
    [MIXCUT_PIPELINES.video.flip]: base,
    [MIXCUT_PIPELINES.video.smartClip]: base,
    [MIXCUT_PIPELINES.video.speed]: base,
    [MIXCUT_PIPELINES.video.masking]: base,
    //#endregion

    //#region ~ 口播字幕
    [MIXCUT_PIPELINES.narrationText.positionOffset]: textPositionOffsetSchema.extend({
      allowHorizontalOffset: z.boolean().default(true),
    }),
    [MIXCUT_PIPELINES.narrationText.fontFamily]: textFontFamilySchema,
    [MIXCUT_PIPELINES.narrationText.styledText]: textStyledTextSchema,
    //#endregion

    //#region ~ 全局文字
    [MIXCUT_PIPELINES.globalText.positionOffset]: textPositionOffsetSchema,
    [MIXCUT_PIPELINES.globalText.fontFamily]: textFontFamilySchema,
    [MIXCUT_PIPELINES.globalText.styledText]: textStyledTextSchema,
    //#endregion

    //#region ~ 口播声音
    [MIXCUT_PIPELINES.narrationSound.timbre]: base,
    //#endregion

  } as const
}

export type ParamsForPipeline<TPipeline extends MixcutPipelines> = TPipeline extends keyof typeof MixcutPipelineSchemas.schemaByPipeline
  ? z.infer<(typeof MixcutPipelineSchemas.schemaByPipeline)[TPipeline]>
  : never
